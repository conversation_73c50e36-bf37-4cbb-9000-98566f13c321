import dotenv from 'dotenv';
import Logger from './libs/Logger';
import { TelegramChannelListener } from './services/TelegramChannelListener';

dotenv.config();

async function main(): Promise<void> {
  const listener = new TelegramChannelListener(25709608, '04d256f05a65b51dd646c5a1d58a098d');

  listener.listenChannel((msg) => {
    console.log('🆕 New message:', msg);
  });
}

if (require.main === module) {
  main().catch((error) => {
    Logger.error('Unhandled error:', error);
    process.exit(1);
  });
}
