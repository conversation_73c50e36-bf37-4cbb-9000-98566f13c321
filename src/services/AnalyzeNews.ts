import { TelegramChannelListener } from './TelegramChannelListener';
import promptSync from 'prompt-sync';
import fs from 'fs';
import path from 'path';
import OpenAI from 'openai';
import { NewsItem, NewsAnalysisResult, NewsAnalysisResponse } from '../types';
import TelegramBot from 'node-telegram-bot-api';
import dayjs from 'dayjs';

export default class AnalyzeNews {
  private telegramService;
  private openai: OpenAI;
  private bot: TelegramBot;

  constructor() {
    this.telegramService = new TelegramChannelListener(
      Number(process.env.TELEGRAM_APP_API_ID!),
      process.env.TELEGRAM_APP_HASH!,
    );
    this.bot = new TelegramBot(process.env.TELEGRAM_BOT_ID!, { polling: true });
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY!,
    });
  }

  init() {
    const prompt = promptSync();

    this.telegramService.connect({
      phoneNumber: async () => prompt('📱 Enter phone number: '),
      phoneCode: async () => prompt('📩 Enter the code you received: '),
      onError: (err: any) => console.error(err),
    });

    this.telegramService.listenChannel((msg) => {
      if (!msg.includes(' 🔴')) {
        return;
      }

      const messageData = {
        timestamp: new Date().toISOString(),
        message: msg,
        source: 'telegram_channel',
      };

      const filePath = path.join(process.cwd(), 'new.json');

      let existingData = [];
      if (fs.existsSync(filePath)) {
        const fileContent = fs.readFileSync(filePath, 'utf8');
        existingData = JSON.parse(fileContent);
      }

      existingData.push(messageData);
      fs.writeFileSync(filePath, JSON.stringify(existingData, null, 2));

      console.log('New message:', msg);
    });
  }

  private setupCommands() {
    this.bot.onText(/\/news (\w+)/, async (msg, match) => {
      const chatId = msg.chat.id;
      const period = match?.[1];

      if (!period) {
        this.bot.sendMessage(chatId, 'Usage: /news 1d or /news 1w');
        return;
      }

      try {
        await this.handleNewsCommand(chatId, period);
      } catch (error) {
        this.bot.sendMessage(chatId, `Error: ${error}`);
      }
    });

    this.bot.onText(/\/start/, (msg) => {
      const chatId = msg.chat.id;
      this.bot.sendMessage(chatId, 'Welcome! Use /news 1d for daily news or /news 1w for weekly news analysis.');
    });
  }

  private async handleNewsCommand(chatId: number, period: string) {
    const newsFilePath = path.join(process.cwd(), 'new.json');

    if (!fs.existsSync(newsFilePath)) {
      this.bot.sendMessage(chatId, 'No news data found.');
      return;
    }

    const fileContent = fs.readFileSync(newsFilePath, 'utf8');
    const allNews: NewsItem[] = JSON.parse(fileContent);

    const filteredNews = this.filterNewsByPeriod(allNews, period);

    if (filteredNews.length === 0) {
      this.bot.sendMessage(chatId, `No news found for the last ${period}.`);
      return;
    }

    this.bot.sendMessage(chatId, `Analyzing ${filteredNews.length} news items from the last ${period}...`);

    const result = await this.analyzeNews(filteredNews);

    const message = this.formatAnalysisMessage(result.analysis, result.summary);
    this.bot.sendMessage(chatId, message, { parse_mode: 'HTML' });
  }

  private formatAnalysisMessage(analysis: any, summary: string): string {
    return `
<b>📊 News Analysis Summary</b>

<b>🏛️ Equities:</b> ${analysis.Equities}

<b>💱 FX:</b> ${analysis.FX}

<b>🛢️ Commodities:</b> ${analysis.Commodities}

<b>📈 Rates:</b> ${analysis.Rates}

<b>🌍 Geopolitics:</b> ${analysis.Geopolitics}

<b>🎯 Key Theme:</b> ${analysis.KeyTheme}

<b>📝 Summary:</b>
${summary}
    `.trim();
  }

  private filterNewsByPeriod(news: NewsItem[], period: string): NewsItem[] {
    const now = dayjs();
    let cutoffTime: dayjs.Dayjs;

    switch (period) {
      case '1d':
        cutoffTime = now.subtract(1, 'day');
        break;
      case '1w':
        cutoffTime = now.subtract(1, 'week');
        break;
      default:
        throw new Error('Invalid period. Use 1d or 1w.');
    }

    return news.filter((item) => {
      const itemTime = dayjs(item.timestamp);
      return itemTime.isAfter(cutoffTime);
    });
  }

  async analyzeNews(newsData: NewsItem[]): Promise<NewsAnalysisResult> {
    const systemPrompt = 'You are a macroeconomic and financial markets expert analyst.';

    const userPrompt = `News data (JSON):
${JSON.stringify(newsData, null, 2)}

Nhiệm vụ:
1. Phân loại từng tin tức vào các nhóm sau:
   - Chính sách tiền tệ & lãi suất
   - Dữ liệu kinh tế vĩ mô
   - Thị trường chứng khoán
   - Tiền tệ (FX)
   - Hàng hóa
   - Chính trị & địa chính trị

2. Đánh giá tác động của từng tin tức (Tích cực / Tiêu cực / Trung lập) và xác định các thị trường liên quan (Chứng khoán, FX, Hàng hóa, Lãi suất).

3. Tạo bản tóm tắt trong đúng cấu trúc JSON sau:
   {
     "Equities": "...",
     "FX": "...",
     "Commodities": "...",
     "Rates": "...",
     "Geopolitics": "...",
     "KeyTheme": "..."
   }

4. Viết thêm một đoạn tóm tắt ngắn (3–5 câu) để đọc nhanh nhưng phải đủ ý, nếu cần thiết thì tăng lên 5-10 câu.`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;

      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      const jsonMatch = content.match(/\{[\s\S]*?\}/);
      if (!jsonMatch) {
        throw new Error('No JSON structure found in response');
      }

      const analysis: NewsAnalysisResponse = JSON.parse(jsonMatch[0]);

      const summaryMatch = content.split(jsonMatch[0])[1]?.trim();
      const summary = summaryMatch || 'No summary available';

      return {
        analysis,
        summary,
      };
    } catch (error) {
      throw new Error(`Failed to analyze news: ${error}`);
    }
  }

  async analyzeNewsFromFile(): Promise<NewsAnalysisResult> {
    const newsFilePath = path.join(process.cwd(), 'new.json');

    if (!fs.existsSync(newsFilePath)) {
      throw new Error(`News file not found: ${newsFilePath}`);
    }

    const fileContent = fs.readFileSync(newsFilePath, 'utf8');
    const newsData: NewsItem[] = JSON.parse(fileContent);

    if (!newsData || newsData.length === 0) {
      throw new Error('No news data found in file');
    }

    return this.analyzeNews(newsData);
  }
}
