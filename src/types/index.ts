export type StringNum = string | number;

export interface NewsItem {
  timestamp: string;
  message: string;
  source: string;
}

export interface NewsAnalysisResponse {
  Equities: string;
  FX: string;
  Commodities: string;
  Rates: string;
  Geopolitics: string;
  KeyTheme: string;
}

export interface NewsAnalysisResult {
  analysis: NewsAnalysisResponse;
  summary: string;
}
export * from './enums';
